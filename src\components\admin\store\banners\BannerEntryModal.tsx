import React, { useState, useEffect } from 'react';
import { useMutation } from '@tanstack/react-query';
import { BannersAPI, PromotionalBanner, CreateBannerData, UpdateBannerData } from '@/lib/api/store/banners';
import { ImageUpload, useImageUpload } from '@/components/ui/ImageUpload';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Megaphone, Save, X, Palette, Type, Image, Layers } from 'lucide-react';
import { toast } from 'sonner';

interface BannerEntryModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  storeId: string;
  editingBanner?: PromotionalBanner | null;
}

const contentTypeOptions = [
  { value: 'text', label: 'Text Only', icon: Type },
  { value: 'image', label: 'Image Only', icon: Image },
  { value: 'mixed', label: 'Image + Text', icon: Layers }
];

const animationOptions = [
  { value: 'none', label: 'No Animation' },
  { value: 'fade', label: 'Fade In' },
  { value: 'slide', label: 'Slide In' },
  { value: 'bounce', label: 'Bounce In' },
  { value: 'pulse', label: 'Pulse' }
];

/**
 * Modal for adding/editing promotional banners
 */
export const BannerEntryModal: React.FC<BannerEntryModalProps> = ({
  isOpen,
  onClose,
  onSuccess,
  storeId,
  editingBanner
}) => {
  const [formData, setFormData] = useState({
    title: '',
    subtitle: '',
    content_type: 'text' as const,
    text_content: '',
    cta_text: '',
    cta_url: '',
    banner_image_alt: '',
    background_color: '#ffffff',
    text_color: '#000000',
    animation_type: 'none' as const,
    priority_order: '',
    start_date: '',
    end_date: '',
    is_active: true
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  // Image upload hook
  const {
    imageUrl,
    handleUploadComplete,
    handleUploadError,
    clearImage,
    setImageUrl
  } = useImageUpload({
    bucket: 'store-banner-images',
    folder: storeId,
    maxSizeBytes: 5 * 1024 * 1024, // 5MB
    allowedTypes: ['image/jpeg', 'image/png', 'image/webp', 'image/gif']
  });

  // Initialize form data when editing
  useEffect(() => {
    if (editingBanner) {
      setFormData({
        title: editingBanner.title,
        subtitle: editingBanner.subtitle || '',
        content_type: editingBanner.content_type,
        text_content: editingBanner.text_content || '',
        cta_text: editingBanner.cta_text || '',
        cta_url: editingBanner.cta_url || '',
        banner_image_alt: editingBanner.banner_image_alt || '',
        background_color: editingBanner.background_color || '#ffffff',
        text_color: editingBanner.text_color || '#000000',
        animation_type: editingBanner.animation_type || 'none',
        priority_order: editingBanner.priority_order.toString(),
        start_date: editingBanner.start_date ? editingBanner.start_date.split('T')[0] : '',
        end_date: editingBanner.end_date ? editingBanner.end_date.split('T')[0] : '',
        is_active: editingBanner.is_active
      });
      setImageUrl(editingBanner.banner_image_url || null);
    } else {
      // Reset form for new banner
      setFormData({
        title: '',
        subtitle: '',
        content_type: 'text',
        text_content: '',
        cta_text: '',
        cta_url: '',
        banner_image_alt: '',
        background_color: '#ffffff',
        text_color: '#000000',
        animation_type: 'none',
        priority_order: '',
        start_date: '',
        end_date: '',
        is_active: true
      });
      clearImage();
    }
    setErrors({});
  }, [editingBanner, setImageUrl, clearImage]);

  // Create mutation
  const createMutation = useMutation({
    mutationFn: (data: CreateBannerData) => BannersAPI.createBanner(data),
    onSuccess: () => {
      toast.success('Banner created successfully');
      onSuccess();
    },
    onError: (error) => {
      console.error('Error creating banner:', error);
      toast.error('Failed to create banner');
    }
  });

  // Update mutation
  const updateMutation = useMutation({
    mutationFn: ({ bannerId, data }: { bannerId: string; data: UpdateBannerData }) => 
      BannersAPI.updateBanner(bannerId, data),
    onSuccess: () => {
      toast.success('Banner updated successfully');
      onSuccess();
    },
    onError: (error) => {
      console.error('Error updating banner:', error);
      toast.error('Failed to update banner');
    }
  });

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.title.trim()) {
      newErrors.title = 'Banner title is required';
    } else if (formData.title.length > 100) {
      newErrors.title = 'Title must be 100 characters or less';
    }

    if (formData.subtitle && formData.subtitle.length > 150) {
      newErrors.subtitle = 'Subtitle must be 150 characters or less';
    }

    if (formData.content_type === 'image' && !imageUrl) {
      newErrors.banner_image = 'Image is required for image banners';
    }

    if (formData.content_type === 'text' && !formData.text_content && !formData.subtitle) {
      newErrors.text_content = 'Text content or subtitle is required for text banners';
    }

    if (formData.text_content && formData.text_content.length > 500) {
      newErrors.text_content = 'Text content must be 500 characters or less';
    }

    if (formData.cta_text && formData.cta_text.length > 50) {
      newErrors.cta_text = 'CTA text must be 50 characters or less';
    }

    if (formData.cta_url && !formData.cta_url.match(/^https?:\/\/.+/)) {
      newErrors.cta_url = 'Please enter a valid URL starting with http:// or https://';
    }

    if (formData.start_date && formData.end_date && new Date(formData.start_date) >= new Date(formData.end_date)) {
      newErrors.end_date = 'End date must be after start date';
    }

    if (!editingBanner && formData.priority_order) {
      const priority = parseInt(formData.priority_order);
      if (isNaN(priority) || priority < 1) {
        newErrors.priority_order = 'Priority order must be a positive number';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    const baseData = {
      title: formData.title.trim(),
      subtitle: formData.subtitle.trim() || undefined,
      content_type: formData.content_type,
      text_content: formData.text_content.trim() || undefined,
      cta_text: formData.cta_text.trim() || undefined,
      cta_url: formData.cta_url.trim() || undefined,
      banner_image_url: imageUrl || undefined,
      banner_image_alt: formData.banner_image_alt.trim() || undefined,
      background_color: formData.background_color,
      text_color: formData.text_color,
      animation_type: formData.animation_type === 'none' ? undefined : formData.animation_type,
      start_date: formData.start_date || undefined,
      end_date: formData.end_date || undefined,
      is_active: formData.is_active
    };

    if (editingBanner) {
      // Update existing banner
      updateMutation.mutate({
        bannerId: editingBanner.id,
        data: baseData
      });
    } else {
      // Create new banner
      const createData: CreateBannerData = {
        ...baseData,
        store_id: storeId,
        priority_order: formData.priority_order ? parseInt(formData.priority_order) : undefined
      };
      createMutation.mutate(createData);
    }
  };

  const isLoading = createMutation.isPending || updateMutation.isPending;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Megaphone className="h-5 w-5" />
            {editingBanner ? 'Edit Banner' : 'Create Promotional Banner'}
          </DialogTitle>
          <DialogDescription>
            {editingBanner 
              ? 'Update the banner information and settings'
              : 'Create a new promotional banner for your landing page'
            }
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Title */}
            <div className="space-y-2">
              <Label htmlFor="title">Banner Title *</Label>
              <Input
                id="title"
                value={formData.title}
                onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                placeholder="Enter banner title"
                maxLength={100}
              />
              {errors.title && (
                <p className="text-sm text-red-600">{errors.title}</p>
              )}
            </div>

            {/* Subtitle */}
            <div className="space-y-2">
              <Label htmlFor="subtitle">Subtitle (optional)</Label>
              <Input
                id="subtitle"
                value={formData.subtitle}
                onChange={(e) => setFormData(prev => ({ ...prev, subtitle: e.target.value }))}
                placeholder="Enter subtitle"
                maxLength={150}
              />
              {errors.subtitle && (
                <p className="text-sm text-red-600">{errors.subtitle}</p>
              )}
            </div>
          </div>

          {/* Content Type */}
          <div className="space-y-2">
            <Label htmlFor="content_type">Content Type *</Label>
            <Select
              value={formData.content_type}
              onValueChange={(value: any) => setFormData(prev => ({ ...prev, content_type: value }))}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {contentTypeOptions.map(option => {
                  const Icon = option.icon;
                  return (
                    <SelectItem key={option.value} value={option.value}>
                      <div className="flex items-center gap-2">
                        <Icon className="h-4 w-4" />
                        {option.label}
                      </div>
                    </SelectItem>
                  );
                })}
              </SelectContent>
            </Select>
          </div>

          {/* Text Content (for text and mixed types) */}
          {(formData.content_type === 'text' || formData.content_type === 'mixed') && (
            <div className="space-y-2">
              <Label htmlFor="text_content">Text Content</Label>
              <Textarea
                id="text_content"
                value={formData.text_content}
                onChange={(e) => setFormData(prev => ({ ...prev, text_content: e.target.value }))}
                placeholder="Enter banner text content"
                maxLength={500}
                rows={3}
              />
              <p className="text-xs text-gray-500">
                {formData.text_content.length}/500 characters
              </p>
              {errors.text_content && (
                <p className="text-sm text-red-600">{errors.text_content}</p>
              )}
            </div>
          )}

          {/* Banner Image (for image and mixed types) */}
          {(formData.content_type === 'image' || formData.content_type === 'mixed') && (
            <div className="space-y-2">
              <Label>Banner Image</Label>
              <ImageUpload
                onUploadComplete={handleUploadComplete}
                onUploadError={handleUploadError}
                uploadOptions={{
                  bucket: 'store-banner-images',
                  folder: storeId,
                  maxSizeBytes: 5 * 1024 * 1024,
                  allowedTypes: ['image/jpeg', 'image/png', 'image/webp', 'image/gif']
                }}
                currentImageUrl={imageUrl || undefined}
                placeholder="Upload banner image (recommended: 1200x400px)"
              />
              {errors.banner_image && (
                <p className="text-sm text-red-600">{errors.banner_image}</p>
              )}
            </div>
          )}

          {/* Image Alt Text */}
          {imageUrl && (
            <div className="space-y-2">
              <Label htmlFor="banner_image_alt">Image Alt Text</Label>
              <Input
                id="banner_image_alt"
                value={formData.banner_image_alt}
                onChange={(e) => setFormData(prev => ({ ...prev, banner_image_alt: e.target.value }))}
                placeholder="Describe the image for accessibility"
                maxLength={200}
              />
            </div>
          )}

          {/* Call to Action */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="cta_text">CTA Button Text (optional)</Label>
              <Input
                id="cta_text"
                value={formData.cta_text}
                onChange={(e) => setFormData(prev => ({ ...prev, cta_text: e.target.value }))}
                placeholder="e.g., Shop Now, Learn More"
                maxLength={50}
              />
              {errors.cta_text && (
                <p className="text-sm text-red-600">{errors.cta_text}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="cta_url">CTA URL (optional)</Label>
              <Input
                id="cta_url"
                type="url"
                value={formData.cta_url}
                onChange={(e) => setFormData(prev => ({ ...prev, cta_url: e.target.value }))}
                placeholder="https://example.com"
              />
              {errors.cta_url && (
                <p className="text-sm text-red-600">{errors.cta_url}</p>
              )}
            </div>
          </div>

          {/* Styling */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="background_color">Background Color</Label>
              <div className="flex gap-2">
                <Input
                  id="background_color"
                  type="color"
                  value={formData.background_color}
                  onChange={(e) => setFormData(prev => ({ ...prev, background_color: e.target.value }))}
                  className="w-16 h-10 p-1 border rounded"
                />
                <Input
                  value={formData.background_color}
                  onChange={(e) => setFormData(prev => ({ ...prev, background_color: e.target.value }))}
                  placeholder="#ffffff"
                  className="flex-1"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="text_color">Text Color</Label>
              <div className="flex gap-2">
                <Input
                  id="text_color"
                  type="color"
                  value={formData.text_color}
                  onChange={(e) => setFormData(prev => ({ ...prev, text_color: e.target.value }))}
                  className="w-16 h-10 p-1 border rounded"
                />
                <Input
                  value={formData.text_color}
                  onChange={(e) => setFormData(prev => ({ ...prev, text_color: e.target.value }))}
                  placeholder="#000000"
                  className="flex-1"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="animation_type">Animation</Label>
              <Select
                value={formData.animation_type}
                onValueChange={(value: any) => setFormData(prev => ({ ...prev, animation_type: value }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {animationOptions.map(option => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Scheduling */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="start_date">Start Date (optional)</Label>
              <Input
                id="start_date"
                type="date"
                value={formData.start_date}
                onChange={(e) => setFormData(prev => ({ ...prev, start_date: e.target.value }))}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="end_date">End Date (optional)</Label>
              <Input
                id="end_date"
                type="date"
                value={formData.end_date}
                onChange={(e) => setFormData(prev => ({ ...prev, end_date: e.target.value }))}
              />
              {errors.end_date && (
                <p className="text-sm text-red-600">{errors.end_date}</p>
              )}
            </div>
          </div>

          {/* Priority Order (only for new banners) */}
          {!editingBanner && (
            <div className="space-y-2">
              <Label htmlFor="priority_order">Priority Order (optional)</Label>
              <Input
                id="priority_order"
                type="number"
                min="1"
                value={formData.priority_order}
                onChange={(e) => setFormData(prev => ({ ...prev, priority_order: e.target.value }))}
                placeholder="Leave empty for automatic ordering"
              />
              {errors.priority_order && (
                <p className="text-sm text-red-600">{errors.priority_order}</p>
              )}
            </div>
          )}

          {/* Active Status */}
          <div className="flex items-center space-x-2">
            <Switch
              id="is_active"
              checked={formData.is_active}
              onCheckedChange={(checked) => setFormData(prev => ({ ...prev, is_active: checked }))}
            />
            <Label htmlFor="is_active">
              Active (visible on landing page)
            </Label>
          </div>

          {/* Error Alert */}
          {Object.keys(errors).length > 0 && (
            <Alert variant="destructive">
              <AlertDescription>
                Please fix the errors above before submitting.
              </AlertDescription>
            </Alert>
          )}
        </form>

        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            onClick={onClose}
            disabled={isLoading}
          >
            <X className="h-4 w-4 mr-2" />
            Cancel
          </Button>
          <Button
            type="submit"
            onClick={handleSubmit}
            disabled={isLoading}
          >
            <Save className="h-4 w-4 mr-2" />
            {isLoading ? 'Saving...' : editingBanner ? 'Update Banner' : 'Create Banner'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
