import React, { useState } from 'react';
import { DragDropContext, Droppable, Draggable, DropResult } from '@hello-pangea/dnd';
import { PromotionalBanner } from '@/lib/api/store/banners';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Megaphone, 
  Edit, 
  Trash2, 
  Plus, 
  GripVertical,
  ExternalLink,
  Eye,
  EyeOff,
  Calendar,
  Image,
  Type,
  Layers
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface BannerManagementGridProps {
  banners: PromotionalBanner[];
  onEdit: (banner: PromotionalBanner) => void;
  onDelete: (bannerId: string) => void;
  onReorder: (banners: { id: string; priority_order: number }[]) => void;
  onAdd: () => void;
  isReordering?: boolean;
}

const contentTypeConfig = {
  text: { label: 'Text', icon: Type, color: 'bg-blue-500' },
  image: { label: 'Image', icon: Image, color: 'bg-green-500' },
  mixed: { label: 'Mixed', icon: Layers, color: 'bg-purple-500' }
};

/**
 * Drag-and-drop grid for managing promotional banners
 */
export const BannerManagementGrid: React.FC<BannerManagementGridProps> = ({
  banners,
  onEdit,
  onDelete,
  onReorder,
  onAdd,
  isReordering = false
}) => {
  const [draggedItem, setDraggedItem] = useState<string | null>(null);

  const handleDragStart = (start: any) => {
    setDraggedItem(start.draggableId);
  };

  const handleDragEnd = (result: DropResult) => {
    setDraggedItem(null);

    if (!result.destination) {
      return;
    }

    const sourceIndex = result.source.index;
    const destinationIndex = result.destination.index;

    if (sourceIndex === destinationIndex) {
      return;
    }

    // Create new order based on drag result
    const newBanners = [...banners];
    const [movedBanner] = newBanners.splice(sourceIndex, 1);
    newBanners.splice(destinationIndex, 0, movedBanner);

    // Update priority orders
    const reorderedBanners = newBanners.map((banner, index) => ({
      id: banner.id,
      priority_order: index + 1
    }));

    onReorder(reorderedBanners);
  };

  const EmptyState: React.FC = () => (
    <Card className="border-2 border-dashed border-gray-300 hover:border-gray-400 transition-colors">
      <CardContent className="p-8 text-center">
        <div className="space-y-4">
          <div className="w-16 h-16 mx-auto bg-gray-100 rounded-lg flex items-center justify-center">
            <Megaphone className="h-8 w-8 text-gray-400" />
          </div>
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Banners Created</h3>
            <p className="text-sm text-gray-500 mb-4">
              Create promotional banners to showcase special offers and announcements
            </p>
          </div>
          <Button onClick={onAdd} className="w-full">
            <Plus className="h-4 w-4 mr-2" />
            Create Your First Banner
          </Button>
        </div>
      </CardContent>
    </Card>
  );

  const BannerCard: React.FC<{ banner: PromotionalBanner; index: number }> = ({ banner, index }) => {
    const contentTypeInfo = contentTypeConfig[banner.content_type];
    const ContentTypeIcon = contentTypeInfo.icon;

    // Check if banner is scheduled
    const now = new Date();
    const startDate = banner.start_date ? new Date(banner.start_date) : null;
    const endDate = banner.end_date ? new Date(banner.end_date) : null;
    const isScheduled = startDate && startDate > now;
    const isExpired = endDate && endDate <= now;
    const isActive = banner.is_active && !isExpired && (!startDate || startDate <= now);

    return (
      <Draggable draggableId={banner.id} index={index}>
        {(provided, snapshot) => (
          <Card
            ref={provided.innerRef}
            {...provided.draggableProps}
            className={cn(
              "transition-all duration-200",
              snapshot.isDragging && "shadow-lg rotate-1 scale-105",
              !isActive && "opacity-60"
            )}
          >
            <CardContent className="p-4">
              {/* Drag Handle & Status */}
              <div
                {...provided.dragHandleProps}
                className="flex items-center justify-between mb-3 cursor-grab active:cursor-grabbing"
              >
                <div className="flex items-center gap-2">
                  <GripVertical className="h-4 w-4 text-gray-400" />
                  <span className="text-sm font-medium text-gray-600">
                    Priority {banner.priority_order}
                  </span>
                </div>
                <div className="flex items-center gap-1">
                  {isActive ? (
                    <Eye className="h-4 w-4 text-green-500" />
                  ) : isScheduled ? (
                    <Calendar className="h-4 w-4 text-blue-500" />
                  ) : isExpired ? (
                    <Calendar className="h-4 w-4 text-red-500" />
                  ) : (
                    <EyeOff className="h-4 w-4 text-gray-400" />
                  )}
                  {banner.cta_url && (
                    <ExternalLink className="h-4 w-4 text-blue-500" />
                  )}
                </div>
              </div>

              {/* Banner Preview */}
              <div className="relative mb-3">
                <div 
                  className="aspect-[3/1] bg-gray-100 rounded-lg overflow-hidden"
                  style={{
                    backgroundColor: banner.background_color || '#f3f4f6',
                    backgroundImage: banner.banner_image_url ? `url(${banner.banner_image_url})` : undefined,
                    backgroundSize: 'cover',
                    backgroundPosition: 'center'
                  }}
                >
                  <div 
                    className="w-full h-full flex items-center justify-center p-4"
                    style={{ color: banner.text_color || '#000000' }}
                  >
                    <div className="text-center">
                      <h4 className="font-semibold text-sm line-clamp-1 mb-1">
                        {banner.title}
                      </h4>
                      {banner.subtitle && (
                        <p className="text-xs opacity-80 line-clamp-1">
                          {banner.subtitle}
                        </p>
                      )}
                    </div>
                  </div>
                </div>

                {/* Content Type Badge */}
                <Badge 
                  className={cn(
                    "absolute top-2 left-2 text-xs",
                    contentTypeInfo.color,
                    "text-white border-0"
                  )}
                >
                  <ContentTypeIcon className="h-3 w-3 mr-1" />
                  {contentTypeInfo.label}
                </Badge>

                {/* Status Badge */}
                <Badge 
                  variant={isActive ? "default" : isScheduled ? "secondary" : "outline"}
                  className="absolute top-2 right-2 text-xs"
                >
                  {isActive ? "Active" : isScheduled ? "Scheduled" : isExpired ? "Expired" : "Inactive"}
                </Badge>
              </div>

              {/* Banner Info */}
              <div className="space-y-2 mb-4">
                <h4 className="font-semibold text-sm line-clamp-1">
                  {banner.title}
                </h4>
                
                {banner.text_content && (
                  <p className="text-xs text-gray-600 line-clamp-2">
                    {banner.text_content}
                  </p>
                )}

                {/* Schedule Info */}
                {(banner.start_date || banner.end_date) && (
                  <div className="text-xs text-gray-500">
                    {banner.start_date && (
                      <div>Starts: {new Date(banner.start_date).toLocaleDateString()}</div>
                    )}
                    {banner.end_date && (
                      <div>Ends: {new Date(banner.end_date).toLocaleDateString()}</div>
                    )}
                  </div>
                )}
              </div>

              {/* Action Buttons */}
              <div className="flex gap-2">
                <Button
                  onClick={() => onEdit(banner)}
                  variant="outline"
                  size="sm"
                  className="flex-1"
                >
                  <Edit className="h-3 w-3 mr-1" />
                  Edit
                </Button>
                <Button
                  onClick={() => onDelete(banner.id)}
                  variant="outline"
                  size="sm"
                  className="text-red-600 hover:text-red-700 hover:bg-red-50"
                >
                  <Trash2 className="h-3 w-3" />
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
      </Draggable>
    );
  };

  if (banners.length === 0) {
    return <EmptyState />;
  }

  return (
    <div className="space-y-4">
      {/* Status Info */}
      <div className="flex items-center justify-between text-sm text-gray-600">
        <span>{banners.length} banner(s) created</span>
        {isReordering && (
          <span className="text-blue-600">Updating order...</span>
        )}
      </div>

      {/* Grid */}
      <DragDropContext onDragStart={handleDragStart} onDragEnd={handleDragEnd}>
        <Droppable droppableId="banner-grid">
          {(provided) => (
            <div
              ref={provided.innerRef}
              {...provided.droppableProps}
              className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"
            >
              {banners.map((banner, index) => (
                <BannerCard key={banner.id} banner={banner} index={index} />
              ))}
              {provided.placeholder}
            </div>
          )}
        </Droppable>
      </DragDropContext>

      {/* Add Banner Button */}
      <Card className="border-2 border-dashed border-gray-300 hover:border-gray-400 transition-colors">
        <CardContent className="p-6 text-center">
          <Button onClick={onAdd} variant="outline" className="w-full">
            <Plus className="h-4 w-4 mr-2" />
            Add New Banner
          </Button>
        </CardContent>
      </Card>
    </div>
  );
};
