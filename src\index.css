@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Lora:ital,wght@0,400;0,500;0,600;0,700;1,400;1,500;1,600;1,700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 43 42% 94%;
    --foreground: 30 28% 18%;

    --card: 39 54% 94%;
    --card-foreground: 30 28% 18%;

    --popover: 39 54% 94%;
    --popover-foreground: 30 28% 18%;

    --primary: 21 38% 57%;
    --primary-foreground: 39 54% 94%;

    --secondary: 85 8% 57%;
    --secondary-foreground: 39 54% 94%;

    --muted: 39 15% 85%;
    --muted-foreground: 30 10% 35%;

    --accent: 16 45% 57%;
    --accent-foreground: 39 54% 94%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 30 15% 80%;
    --input: 30 15% 80%;
    --ring: 21 38% 57%;

    --radius: 0.5rem;

    --sidebar-background: 39 54% 94%;
    --sidebar-foreground: 30 28% 18%;
    --sidebar-primary: 21 38% 57%;
    --sidebar-primary-foreground: 39 54% 94%;
    --sidebar-accent: 39 15% 90%;
    --sidebar-accent-foreground: 30 28% 18%;
    --sidebar-border: 30 15% 80%;
    --sidebar-ring: 21 38% 57%;
  }

  .dark {
    --background: 30 15% 12%;
    --foreground: 39 30% 90%;

    --card: 30 15% 15%;
    --card-foreground: 39 30% 90%;

    --popover: 30 15% 15%;
    --popover-foreground: 39 30% 90%;

    --primary: 21 38% 47%;
    --primary-foreground: 39 54% 94%;

    --secondary: 85 12% 47%;
    --secondary-foreground: 39 54% 94%;

    --muted: 30 15% 25%;
    --muted-foreground: 39 20% 70%;

    --accent: 16 45% 47%;
    --accent-foreground: 39 54% 94%;

    --destructive: 0 62.8% 40.6%;
    --destructive-foreground: 39 54% 94%;

    --border: 30 15% 25%;
    --input: 30 15% 25%;
    --ring: 21 38% 47%;

    --sidebar-background: 30 15% 15%;
    --sidebar-foreground: 39 30% 90%;
    --sidebar-primary: 21 38% 47%;
    --sidebar-primary-foreground: 39 54% 94%;
    --sidebar-accent: 30 15% 20%;
    --sidebar-accent-foreground: 39 30% 90%;
    --sidebar-border: 30 15% 25%;
    --sidebar-ring: 21 38% 47%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-serif;
  }
}

.book-card {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.book-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(139, 110, 79, 0.2);
}

.chat-container {
  height: calc(100vh - 300px);
}

@media (max-width: 768px) {
  .chat-container {
    height: calc(100vh - 250px);
  }
}

/* Handwritten font for chat messages */
.handwritten {
  font-family: 'Lora', serif;
}

/* Animation for fading in elements */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

/* Message delivery status animations */
.message-sent {
  opacity: 0.7;
}

.message-delivered {
  opacity: 0.8;
}

.message-read {
  opacity: 1;
}

/* Hide scrollbar but allow scrolling */
.scrollbar-hide {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;  /* Chrome, Safari and Opera */
}

/* Custom scrollbar styles */
.scrollbar-thin::-webkit-scrollbar {
  width: 4px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: transparent;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background-color: rgba(139, 110, 79, 0.3);
  border-radius: 20px;
}

/* Text wrapping utilities */
.break-words {
  word-break: break-word;
}

.overflow-wrap-anywhere {
  overflow-wrap: anywhere;
}

/* Line clamp utilities */
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* User tier badge gradients */
.bg-silver-gradient {
  background: linear-gradient(135deg, #e6e6e6 0%, #c0c0c0 50%, #d9d9d9 100%);
}

.bg-gold-gradient {
  background: linear-gradient(135deg, #f7e084 0%, #ffd700 50%, #f9e076 100%);
}
