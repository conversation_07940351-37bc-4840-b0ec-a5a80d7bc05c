import React, { useState, useEffect } from 'react';
import { useMutation } from '@tanstack/react-query';
import { CarouselAPI, CarouselItem, CreateCarouselItemData, UpdateCarouselItemData } from '@/lib/api/store/carousel';
import { ImageUpload, useImageUpload } from '@/components/ui/ImageUpload';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { BookOpen, Save, X } from 'lucide-react';
import { toast } from 'sonner';

interface BookEntryModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  storeId: string;
  editingItem?: CarouselItem | null;
}

const featuredBadgeOptions = [
  { value: 'none', label: 'No Badge' },
  { value: 'new_arrival', label: 'New Arrival' },
  { value: 'staff_pick', label: 'Staff Pick' },
  { value: 'bestseller', label: 'Bestseller' },
  { value: 'on_sale', label: 'On Sale' },
  { value: 'featured', label: 'Featured' }
];

/**
 * Modal for adding/editing carousel book entries
 */
export const BookEntryModal: React.FC<BookEntryModalProps> = ({
  isOpen,
  onClose,
  onSuccess,
  storeId,
  editingItem
}) => {
  const [formData, setFormData] = useState({
    position: '',
    book_title: '',
    book_author: '',
    book_isbn: '',
    custom_description: '',
    featured_badge: 'none' as const,
    overlay_text: '',
    book_image_alt: '',
    click_destination_url: '',
    is_active: true
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  // Image upload hook
  const {
    imageUrl,
    handleUploadComplete,
    handleUploadError,
    clearImage,
    setImageUrl
  } = useImageUpload({
    bucket: 'store-carousel-images',
    folder: storeId,
    maxSizeBytes: 3 * 1024 * 1024, // 3MB
    allowedTypes: ['image/jpeg', 'image/png', 'image/webp', 'image/gif']
  });

  // Initialize form data when editing
  useEffect(() => {
    if (editingItem) {
      setFormData({
        position: editingItem.position.toString(),
        book_title: editingItem.book_title,
        book_author: editingItem.book_author,
        book_isbn: editingItem.book_isbn || '',
        custom_description: editingItem.custom_description || '',
        featured_badge: editingItem.featured_badge || 'none',
        overlay_text: editingItem.overlay_text || '',
        book_image_alt: editingItem.book_image_alt || '',
        click_destination_url: editingItem.click_destination_url || '',
        is_active: editingItem.is_active
      });
      setImageUrl(editingItem.book_image_url || null);
    } else {
      // Reset form for new item
      setFormData({
        position: '',
        book_title: '',
        book_author: '',
        book_isbn: '',
        custom_description: '',
        featured_badge: 'none',
        overlay_text: '',
        book_image_alt: '',
        click_destination_url: '',
        is_active: true
      });
      clearImage();
    }
    setErrors({});
  }, [editingItem, setImageUrl, clearImage]);

  // Create mutation
  const createMutation = useMutation({
    mutationFn: (data: CreateCarouselItemData) => CarouselAPI.createCarouselItem(data),
    onSuccess: () => {
      toast.success('Book added to carousel');
      onSuccess();
    },
    onError: (error) => {
      console.error('Error creating carousel item:', error);
      toast.error('Failed to add book to carousel');
    }
  });

  // Update mutation
  const updateMutation = useMutation({
    mutationFn: ({ itemId, data }: { itemId: string; data: UpdateCarouselItemData }) => 
      CarouselAPI.updateCarouselItem(itemId, data),
    onSuccess: () => {
      toast.success('Book updated successfully');
      onSuccess();
    },
    onError: (error) => {
      console.error('Error updating carousel item:', error);
      toast.error('Failed to update book');
    }
  });

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.book_title.trim()) {
      newErrors.book_title = 'Book title is required';
    } else if (formData.book_title.length > 200) {
      newErrors.book_title = 'Book title must be 200 characters or less';
    }

    if (!formData.book_author.trim()) {
      newErrors.book_author = 'Book author is required';
    } else if (formData.book_author.length > 100) {
      newErrors.book_author = 'Book author must be 100 characters or less';
    }

    if (formData.book_isbn && formData.book_isbn.length > 20) {
      newErrors.book_isbn = 'ISBN must be 20 characters or less';
    }

    if (formData.custom_description && formData.custom_description.length > 300) {
      newErrors.custom_description = 'Description must be 300 characters or less';
    }

    if (formData.overlay_text && formData.overlay_text.length > 100) {
      newErrors.overlay_text = 'Overlay text must be 100 characters or less';
    }

    if (formData.book_image_alt && formData.book_image_alt.length > 200) {
      newErrors.book_image_alt = 'Alt text must be 200 characters or less';
    }

    if (formData.click_destination_url && !formData.click_destination_url.match(/^https?:\/\/.+/)) {
      newErrors.click_destination_url = 'Please enter a valid URL starting with http:// or https://';
    }

    if (!editingItem && formData.position) {
      const pos = parseInt(formData.position);
      if (isNaN(pos) || pos < 1 || pos > 6) {
        newErrors.position = 'Position must be between 1 and 6';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    const baseData = {
      book_title: formData.book_title.trim(),
      book_author: formData.book_author.trim(),
      book_isbn: formData.book_isbn.trim() || undefined,
      custom_description: formData.custom_description.trim() || undefined,
      featured_badge: formData.featured_badge === 'none' ? undefined : formData.featured_badge,
      overlay_text: formData.overlay_text.trim() || undefined,
      book_image_url: imageUrl || undefined,
      book_image_alt: formData.book_image_alt.trim() || undefined,
      click_destination_url: formData.click_destination_url.trim() || undefined,
      is_active: formData.is_active
    };

    if (editingItem) {
      // Update existing item
      updateMutation.mutate({
        itemId: editingItem.id,
        data: baseData
      });
    } else {
      // Create new item
      const createData: CreateCarouselItemData = {
        ...baseData,
        store_id: storeId,
        position: formData.position ? parseInt(formData.position) : undefined as any
      };
      createMutation.mutate(createData);
    }
  };

  const isLoading = createMutation.isPending || updateMutation.isPending;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <BookOpen className="h-5 w-5" />
            {editingItem ? 'Edit Book' : 'Add Book to Carousel'}
          </DialogTitle>
          <DialogDescription>
            {editingItem 
              ? 'Update the book information and settings'
              : 'Add a new book to your featured carousel (up to 6 books)'
            }
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Position (only for new items) */}
          {!editingItem && (
            <div className="space-y-2">
              <Label htmlFor="position">Position (1-6)</Label>
              <Input
                id="position"
                type="number"
                min="1"
                max="6"
                value={formData.position}
                onChange={(e) => setFormData(prev => ({ ...prev, position: e.target.value }))}
                placeholder="Leave empty for next available position"
              />
              {errors.position && (
                <p className="text-sm text-red-600">{errors.position}</p>
              )}
            </div>
          )}

          {/* Book Title */}
          <div className="space-y-2">
            <Label htmlFor="book_title">Book Title *</Label>
            <Input
              id="book_title"
              value={formData.book_title}
              onChange={(e) => setFormData(prev => ({ ...prev, book_title: e.target.value }))}
              placeholder="Enter book title"
              maxLength={200}
            />
            {errors.book_title && (
              <p className="text-sm text-red-600">{errors.book_title}</p>
            )}
          </div>

          {/* Book Author */}
          <div className="space-y-2">
            <Label htmlFor="book_author">Book Author *</Label>
            <Input
              id="book_author"
              value={formData.book_author}
              onChange={(e) => setFormData(prev => ({ ...prev, book_author: e.target.value }))}
              placeholder="Enter author name"
              maxLength={100}
            />
            {errors.book_author && (
              <p className="text-sm text-red-600">{errors.book_author}</p>
            )}
          </div>

          {/* Book ISBN */}
          <div className="space-y-2">
            <Label htmlFor="book_isbn">ISBN (optional)</Label>
            <Input
              id="book_isbn"
              value={formData.book_isbn}
              onChange={(e) => setFormData(prev => ({ ...prev, book_isbn: e.target.value }))}
              placeholder="Enter ISBN"
              maxLength={20}
            />
            {errors.book_isbn && (
              <p className="text-sm text-red-600">{errors.book_isbn}</p>
            )}
          </div>

          {/* Custom Description */}
          <div className="space-y-2">
            <Label htmlFor="custom_description">Custom Description (optional)</Label>
            <Textarea
              id="custom_description"
              value={formData.custom_description}
              onChange={(e) => setFormData(prev => ({ ...prev, custom_description: e.target.value }))}
              placeholder="Add a custom description for this book"
              maxLength={300}
              rows={3}
            />
            <p className="text-xs text-gray-500">
              {formData.custom_description.length}/300 characters
            </p>
            {errors.custom_description && (
              <p className="text-sm text-red-600">{errors.custom_description}</p>
            )}
          </div>

          {/* Featured Badge */}
          <div className="space-y-2">
            <Label htmlFor="featured_badge">Featured Badge</Label>
            <Select
              value={formData.featured_badge}
              onValueChange={(value: any) => setFormData(prev => ({ ...prev, featured_badge: value }))}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {featuredBadgeOptions.map(option => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Overlay Text */}
          <div className="space-y-2">
            <Label htmlFor="overlay_text">Overlay Text (optional)</Label>
            <Input
              id="overlay_text"
              value={formData.overlay_text}
              onChange={(e) => setFormData(prev => ({ ...prev, overlay_text: e.target.value }))}
              placeholder="Text to display over the book cover"
              maxLength={100}
            />
            {errors.overlay_text && (
              <p className="text-sm text-red-600">{errors.overlay_text}</p>
            )}
          </div>

          {/* Book Cover Image */}
          <div className="space-y-2">
            <Label>Book Cover Image</Label>
            <ImageUpload
              onUploadComplete={handleUploadComplete}
              onUploadError={handleUploadError}
              uploadOptions={{
                bucket: 'store-carousel-images',
                folder: storeId,
                maxSizeBytes: 3 * 1024 * 1024,
                allowedTypes: ['image/jpeg', 'image/png', 'image/webp', 'image/gif']
              }}
              currentImageUrl={imageUrl || undefined}
              placeholder="Upload book cover image"
            />
          </div>

          {/* Image Alt Text */}
          {imageUrl && (
            <div className="space-y-2">
              <Label htmlFor="book_image_alt">Image Alt Text</Label>
              <Input
                id="book_image_alt"
                value={formData.book_image_alt}
                onChange={(e) => setFormData(prev => ({ ...prev, book_image_alt: e.target.value }))}
                placeholder="Describe the image for accessibility"
                maxLength={200}
              />
              {errors.book_image_alt && (
                <p className="text-sm text-red-600">{errors.book_image_alt}</p>
              )}
            </div>
          )}

          {/* Click Destination URL */}
          <div className="space-y-2">
            <Label htmlFor="click_destination_url">Click Destination URL (optional)</Label>
            <Input
              id="click_destination_url"
              type="url"
              value={formData.click_destination_url}
              onChange={(e) => setFormData(prev => ({ ...prev, click_destination_url: e.target.value }))}
              placeholder="https://example.com/book-page"
            />
            {errors.click_destination_url && (
              <p className="text-sm text-red-600">{errors.click_destination_url}</p>
            )}
          </div>

          {/* Active Status */}
          <div className="flex items-center space-x-2">
            <Switch
              id="is_active"
              checked={formData.is_active}
              onCheckedChange={(checked) => setFormData(prev => ({ ...prev, is_active: checked }))}
            />
            <Label htmlFor="is_active">
              Active (visible on landing page)
            </Label>
          </div>

          {/* Error Alert */}
          {Object.keys(errors).length > 0 && (
            <Alert variant="destructive">
              <AlertDescription>
                Please fix the errors above before submitting.
              </AlertDescription>
            </Alert>
          )}
        </form>

        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            onClick={onClose}
            disabled={isLoading}
          >
            <X className="h-4 w-4 mr-2" />
            Cancel
          </Button>
          <Button
            type="submit"
            onClick={handleSubmit}
            disabled={isLoading}
          >
            <Save className="h-4 w-4 mr-2" />
            {isLoading ? 'Saving...' : editingItem ? 'Update Book' : 'Add Book'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
