import { supabase } from '@/lib/supabase';

export interface PromotionalBanner {
  id: string;
  store_id: string;
  title: string;
  subtitle?: string;
  content_type: 'text' | 'image' | 'mixed';
  text_content?: string;
  cta_text?: string;
  cta_url?: string;
  banner_image_url?: string;
  banner_image_alt?: string;
  background_color?: string;
  text_color?: string;
  animation_type?: 'none' | 'fade' | 'slide' | 'bounce' | 'pulse';
  priority_order: number;
  start_date?: string;
  end_date?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface CreateBannerData {
  store_id: string;
  title: string;
  subtitle?: string;
  content_type: 'text' | 'image' | 'mixed';
  text_content?: string;
  cta_text?: string;
  cta_url?: string;
  banner_image_url?: string;
  banner_image_alt?: string;
  background_color?: string;
  text_color?: string;
  animation_type?: 'none' | 'fade' | 'slide' | 'bounce' | 'pulse';
  priority_order?: number;
  start_date?: string;
  end_date?: string;
  is_active?: boolean;
}

export interface UpdateBannerData {
  title?: string;
  subtitle?: string;
  content_type?: 'text' | 'image' | 'mixed';
  text_content?: string;
  cta_text?: string;
  cta_url?: string;
  banner_image_url?: string;
  banner_image_alt?: string;
  background_color?: string;
  text_color?: string;
  animation_type?: 'none' | 'fade' | 'slide' | 'bounce' | 'pulse';
  priority_order?: number;
  start_date?: string;
  end_date?: string;
  is_active?: boolean;
}

/**
 * Promotional Banners API service for managing store marketing banners
 */
export class BannersAPI {
  /**
   * Get all promotional banners for a store
   */
  static async getBanners(storeId: string): Promise<PromotionalBanner[]> {
    const { data, error } = await supabase
      .from('store_promotional_banners')
      .select('*')
      .eq('store_id', storeId)
      .order('priority_order', { ascending: true });

    if (error) {
      console.error('Error fetching promotional banners:', error);
      throw new Error('Failed to fetch promotional banners');
    }

    return data || [];
  }

  /**
   * Get active promotional banners for a store (for public display)
   */
  static async getActiveBanners(storeId: string): Promise<PromotionalBanner[]> {
    const now = new Date().toISOString();
    
    const { data, error } = await supabase
      .from('store_promotional_banners')
      .select('*')
      .eq('store_id', storeId)
      .eq('is_active', true)
      .or(`start_date.is.null,start_date.lte.${now}`)
      .or(`end_date.is.null,end_date.gt.${now}`)
      .order('priority_order', { ascending: true })
      .limit(5);

    if (error) {
      console.error('Error fetching active promotional banners:', error);
      throw new Error('Failed to fetch promotional banners');
    }

    return data || [];
  }

  /**
   * Get a single promotional banner by ID
   */
  static async getBanner(bannerId: string): Promise<PromotionalBanner | null> {
    const { data, error } = await supabase
      .from('store_promotional_banners')
      .select('*')
      .eq('id', bannerId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return null; // Banner not found
      }
      console.error('Error fetching promotional banner:', error);
      throw new Error('Failed to fetch promotional banner');
    }

    return data;
  }

  /**
   * Create a new promotional banner
   */
  static async createBanner(bannerData: CreateBannerData): Promise<PromotionalBanner> {
    // Get next priority order if not specified
    let priorityOrder = bannerData.priority_order;
    if (priorityOrder === undefined) {
      priorityOrder = await this.getNextPriorityOrder(bannerData.store_id);
    }

    const { data, error } = await supabase
      .from('store_promotional_banners')
      .insert({
        ...bannerData,
        priority_order: priorityOrder,
        is_active: bannerData.is_active ?? true
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating promotional banner:', error);
      throw new Error('Failed to create promotional banner');
    }

    return data;
  }

  /**
   * Update a promotional banner
   */
  static async updateBanner(
    bannerId: string, 
    updates: UpdateBannerData
  ): Promise<PromotionalBanner> {
    const { data, error } = await supabase
      .from('store_promotional_banners')
      .update(updates)
      .eq('id', bannerId)
      .select()
      .single();

    if (error) {
      console.error('Error updating promotional banner:', error);
      throw new Error('Failed to update promotional banner');
    }

    return data;
  }

  /**
   * Delete a promotional banner
   */
  static async deleteBanner(bannerId: string): Promise<void> {
    const { error } = await supabase
      .from('store_promotional_banners')
      .delete()
      .eq('id', bannerId);

    if (error) {
      console.error('Error deleting promotional banner:', error);
      throw new Error('Failed to delete promotional banner');
    }
  }

  /**
   * Reorder promotional banners
   */
  static async reorderBanners(
    storeId: string, 
    bannerPositions: { id: string; priority_order: number }[]
  ): Promise<void> {
    // Use a transaction to update all positions atomically
    for (const update of bannerPositions) {
      const { error } = await supabase
        .from('store_promotional_banners')
        .update({ priority_order: update.priority_order })
        .eq('id', update.id)
        .eq('store_id', storeId);

      if (error) {
        console.error('Error updating banner priority order:', error);
        throw new Error('Failed to reorder promotional banners');
      }
    }
  }

  /**
   * Get next available priority order for a store
   */
  static async getNextPriorityOrder(storeId: string): Promise<number> {
    const { data, error } = await supabase
      .from('store_promotional_banners')
      .select('priority_order')
      .eq('store_id', storeId)
      .order('priority_order', { ascending: false })
      .limit(1);

    if (error) {
      console.error('Error getting next priority order:', error);
      return 1;
    }

    if (!data || data.length === 0) {
      return 1;
    }

    return data[0].priority_order + 1;
  }

  /**
   * Check if priority order is available
   */
  static async isPriorityOrderAvailable(storeId: string, priorityOrder: number): Promise<boolean> {
    const { data, error } = await supabase
      .from('store_promotional_banners')
      .select('id')
      .eq('store_id', storeId)
      .eq('priority_order', priorityOrder)
      .limit(1);

    if (error) {
      console.error('Error checking priority order availability:', error);
      return false;
    }

    return !data || data.length === 0;
  }

  /**
   * Get banners scheduled for a specific date range
   */
  static async getBannersInDateRange(
    storeId: string, 
    startDate: string, 
    endDate: string
  ): Promise<PromotionalBanner[]> {
    const { data, error } = await supabase
      .from('store_promotional_banners')
      .select('*')
      .eq('store_id', storeId)
      .or(`start_date.is.null,start_date.lte.${endDate}`)
      .or(`end_date.is.null,end_date.gte.${startDate}`)
      .order('priority_order', { ascending: true });

    if (error) {
      console.error('Error fetching banners in date range:', error);
      throw new Error('Failed to fetch scheduled banners');
    }

    return data || [];
  }

  /**
   * Activate/deactivate banner based on schedule
   */
  static async updateBannerScheduleStatus(storeId: string): Promise<void> {
    const now = new Date().toISOString();

    // Deactivate expired banners
    const { error: deactivateError } = await supabase
      .from('store_promotional_banners')
      .update({ is_active: false })
      .eq('store_id', storeId)
      .not('end_date', 'is', null)
      .lte('end_date', now)
      .eq('is_active', true);

    if (deactivateError) {
      console.error('Error deactivating expired banners:', deactivateError);
    }

    // Activate scheduled banners
    const { error: activateError } = await supabase
      .from('store_promotional_banners')
      .update({ is_active: true })
      .eq('store_id', storeId)
      .or(`start_date.is.null,start_date.lte.${now}`)
      .or(`end_date.is.null,end_date.gt.${now}`)
      .eq('is_active', false);

    if (activateError) {
      console.error('Error activating scheduled banners:', activateError);
    }
  }
}
